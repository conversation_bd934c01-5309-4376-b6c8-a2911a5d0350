import { BaseApiClient } from './base-client';
import { API_ENDPOINTS } from './config';
import { handleApiError as handleError } from './errors';
import { withDeduplication } from '@/lib/utils/request-deduplication';
import {
  CreateProductData,
  CreateBaleData,
  CreateProductOrBaleData,
  CreateProductResponse,
  Product,
  ProductOrBale,
  ProductType
} from '@/lib/types/products';

// Create API client instance
const apiClient = new BaseApiClient();

const productsApiBase = {
  // Create a new product or bale (unified endpoint)
  createProduct: async (data: CreateProductOrBaleData): Promise<CreateProductResponse> => {
    try {
      const formData = new FormData();

      // Add type field first
      formData.append('type', data.type);

      // Add common fields
      formData.append('name', data.name);
      formData.append('description', data.description);
      formData.append('basePrice', data.basePrice.toString());

      // Add type-specific fields
      if (data.type === 'product') {
        const productData = data as CreateProductData;
        formData.append('brand', productData.brand);
        formData.append('category', productData.category);
        formData.append('gender', productData.gender);
      } else if (data.type === 'bale') {
        const baleData = data as CreateBaleData;
        console.log('Bale data received in API:', baleData);
        console.log('Bale gender field:', baleData.gender);

        formData.append('country', baleData.country);
        formData.append('totalItems', baleData.totalItems.toString());
        formData.append('weight', baleData.weight.toString());
        formData.append('condition', baleData.condition);

        // Ensure gender field is present and valid
        if (baleData.gender) {
          formData.append('gender', baleData.gender);
          console.log('Gender field added to FormData:', baleData.gender);
        } else {
          console.error('Gender field is missing or invalid:', baleData.gender);
        }

        // Handle category for bales - use first related category as main category
        if (baleData.relatedCategories && baleData.relatedCategories.length > 0) {
          formData.append('category', baleData.relatedCategories[0]);
          console.log('Category field added to FormData:', baleData.relatedCategories[0]);
        } else {
          console.error('No related categories provided for bale');
          throw new Error('Please select at least one category for the bale');
        }

        // Add dimensions if provided
        if (baleData.dimensions) {
          formData.append('dimensions[length]', baleData.dimensions.length.toString());
          formData.append('dimensions[width]', baleData.dimensions.width.toString());
          formData.append('dimensions[height]', baleData.dimensions.height.toString());
        }
      }

      // Debug: Verify name field is being added
      console.log('Adding name field to FormData:', data.name);
      console.log('FormData has name after adding:', formData.has('name'));
      console.log('FormData name value:', formData.get('name'));

      // Add product images
      data.productImages.forEach((file) => {
        formData.append('productImages', file);
      });

      // Add highlights array (using array notation)
      if (data.highlights && data.highlights.length > 0) {
        data.highlights.forEach((highlight) => {
          formData.append('highlights[]', highlight);
        });
      }

      // Add tags array (using array notation)
      if (data.tags && data.tags.length > 0) {
        data.tags.forEach((tag) => {
          formData.append('tags[]', tag);
        });
      }

      // Add specifications object (using nested notation)
      if (data.specifications) {
        Object.entries(data.specifications).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            value.forEach((item) => {
              formData.append(`specifications[${key}][]`, item);
            });
          } else if (value !== undefined && value !== null && value !== '') {
            formData.append(`specifications[${key}]`, value.toString());
          }
        });
      }

      // Add variations array (using nested array notation)
      if (data.variations && data.variations.length > 0) {
        if (data.type === 'product') {
          const productData = data as CreateProductData;
          productData.variations.forEach((variation, index) => {
            formData.append(`variations[${index}][color]`, variation.color);
            formData.append(`variations[${index}][size]`, variation.size);
            formData.append(`variations[${index}][quantity]`, variation.quantity.toString());
            formData.append(`variations[${index}][price]`, variation.price.toString());

            if (variation.salePrice && variation.salePrice > 0) {
              formData.append(`variations[${index}][salePrice]`, variation.salePrice.toString());
            }
            if (variation.saleStartDate) {
              formData.append(`variations[${index}][saleStartDate]`, variation.saleStartDate);
            }
            if (variation.saleEndDate) {
              formData.append(`variations[${index}][saleEndDate]`, variation.saleEndDate);
            }
          });
        } else if (data.type === 'bale') {
          const baleData = data as CreateBaleData;
          baleData.variations.forEach((variation, index) => {
            formData.append(`variations[${index}][identifier]`, variation.identifier);
            formData.append(`variations[${index}][quantity]`, variation.quantity.toString());
            formData.append(`variations[${index}][price]`, variation.price.toString());

            if (variation.salePrice && variation.salePrice > 0) {
              formData.append(`variations[${index}][salePrice]`, variation.salePrice.toString());
            }
            if (variation.saleStartDate) {
              formData.append(`variations[${index}][saleStartDate]`, variation.saleStartDate);
            }
            if (variation.saleEndDate) {
              formData.append(`variations[${index}][saleEndDate]`, variation.saleEndDate);
            }
          });
        }
      }

      // Add related categories array if provided (for both products and bales)
      if (data.type === 'product') {
        const productData = data as CreateProductData;
        if (productData.relatedCategories && productData.relatedCategories.length > 0) {
          productData.relatedCategories.forEach((categoryId) => {
            formData.append('relatedCategories[]', categoryId);
          });
        }
      } else if (data.type === 'bale') {
        const baleData = data as CreateBaleData;
        if (baleData.relatedCategories && baleData.relatedCategories.length > 0) {
          baleData.relatedCategories.forEach((categoryId) => {
            formData.append('relatedCategories[]', categoryId);
          });
        }
      }

      // Debug: Log all FormData entries
      console.log('=== FormData Debug ===');
      console.log('Complete form data being sent:');
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`${key}: [File] ${value.name} (${value.size} bytes)`);
        } else {
          console.log(`${key}: ${value}`);
        }
      }
      console.log('=== End FormData Debug ===');

      return await apiClient.postFormData<CreateProductResponse>(API_ENDPOINTS.CREATOR_PRODUCTS.CREATE, formData);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Get creator's products with query parameters (supports type filtering)
  getCreatorProducts: async (params?: {
    page?: number;
    limit?: number;
    sort?: string;
    search?: string;
    status?: string;
    fields?: string;
    type?: ProductType; // Add type filtering
  }): Promise<{
    status: string;
    results: number;
    total: number;
    page: number;
    limit: number;
    data: { products: ProductOrBale[] };
  }> => {
    try {
      const queryParams = new URLSearchParams();

      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.sort) queryParams.append('sort', params.sort);
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.fields) queryParams.append('fields', params.fields);
      if (params?.type) queryParams.append('type', params.type); // Add type filtering

      const queryString = queryParams.toString();
      const endpoint = queryString ? `${API_ENDPOINTS.CREATOR_PRODUCTS.LIST}?${queryString}` : API_ENDPOINTS.CREATOR_PRODUCTS.LIST;

      return await apiClient.get<{
        status: string;
        results: number;
        total: number;
        page: number;
        limit: number;
        data: { products: ProductOrBale[] };
      }>(endpoint);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Get product counts for filters (supports type filtering)
  getProductCounts: async (params?: { type?: ProductType }): Promise<{
    status: string;
    data: {
      all: number;
      products?: {
        all: number;
        active: number;
        pending: number;
      };
      bales?: {
        all: number;
        active: number;
        pending: number;
      };
      active: number;
      pending: number;
      rejected: number;
      lowStock: number;
      outOfStock: number;
    };
  }> => {
    try {
      const queryParams = new URLSearchParams();
      if (params?.type) queryParams.append('type', params.type);

      const queryString = queryParams.toString();
      const endpoint = queryString ? `${API_ENDPOINTS.CREATOR_PRODUCTS.COUNTS}?${queryString}` : API_ENDPOINTS.CREATOR_PRODUCTS.COUNTS;

      return await apiClient.get<{
        status: string;
        data: {
          all: number;
          products?: {
            all: number;
            active: number;
            pending: number;
          };
          bales?: {
            all: number;
            active: number;
            pending: number;
          };
          active: number;
          pending: number;
          rejected: number;
          lowStock: number;
          outOfStock: number;
        };
      }>(endpoint);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Get single product by ID (for creators) - supports both products and bales
  getCreatorProduct: async (productId: string): Promise<{
    status: string;
    data: {
      product: ProductOrBale & {
        seo: { keywords: string[] };
        creator: string;
        slug: string;
        totalStock: number;
        availableColors?: string[];
        availableSizes?: string[];
        availableIdentifiers?: string[];
        normalMinPrice: number;
        normalMaxPrice: number;
        discountedMinPrice: number;
        discountedMaxPrice: number;
        hasAnyDiscount: boolean;
        maxDiscountPercentage: number;
        formattedPriceRange: {
          original: { min: number; max: number; isSinglePrice: boolean };
          discounted: { min: number; max: number; isSinglePrice: boolean } | null;
          discountPercentage: number;
        };
        discountEndDate: string | null;
        discountType: string | null;
        hasActivePromotion: boolean;
        bestPromotion: any | null;
      }
    }
  }> => {
    try {
      return await apiClient.get(`${API_ENDPOINTS.CREATOR_PRODUCTS.DETAIL}/${productId}`);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Update product
  updateProduct: async (productId: string, data: Partial<CreateProductData>): Promise<CreateProductResponse> => {
    try {
      const formData = new FormData();

      // Add fields that are being updated
      if (data.name) formData.append('name', data.name);
      if (data.brand) formData.append('brand', data.brand);
      if (data.description) formData.append('description', data.description);
      if (data.basePrice) formData.append('basePrice', data.basePrice.toString());
      if (data.category) formData.append('category', data.category);
      if (data.gender) formData.append('gender', data.gender);

      // Add product images if provided
      if (data.productImages && data.productImages.length > 0) {
        data.productImages.forEach((file) => {
          formData.append('productImages', file);
        });
      }

      // Add highlights if provided
      if (data.highlights && data.highlights.length > 0) {
        data.highlights.forEach((highlight) => {
          formData.append('highlights[]', highlight);
        });
      }

      // Add tags if provided
      if (data.tags && data.tags.length > 0) {
        data.tags.forEach((tag) => {
          formData.append('tags[]', tag);
        });
      }

      // Add specifications if provided
      if (data.specifications) {
        Object.entries(data.specifications).forEach(([key, value]) => {
          if (Array.isArray(value)) {
            value.forEach((item) => {
              formData.append(`specifications[${key}][]`, item);
            });
          } else if (value !== undefined && value !== null && value !== '') {
            formData.append(`specifications[${key}]`, value.toString());
          }
        });
      }

      // Add variations if provided
      if (data.variations && data.variations.length > 0) {
        data.variations.forEach((variation, index) => {
          formData.append(`variations[${index}][color]`, variation.color);
          formData.append(`variations[${index}][size]`, variation.size);
          formData.append(`variations[${index}][quantity]`, variation.quantity.toString());
          formData.append(`variations[${index}][price]`, variation.price.toString());
          
          if (variation.salePrice) {
            formData.append(`variations[${index}][salePrice]`, variation.salePrice.toString());
          }
          if (variation.saleStartDate) {
            formData.append(`variations[${index}][saleStartDate]`, variation.saleStartDate);
          }
          if (variation.saleEndDate) {
            formData.append(`variations[${index}][saleEndDate]`, variation.saleEndDate);
          }
        });
      }

      // Add related categories if provided
      if (data.relatedCategories && data.relatedCategories.length > 0) {
        data.relatedCategories.forEach((categoryId) => {
          formData.append('relatedCategories[]', categoryId);
        });
      }

      return await apiClient.put<CreateProductResponse>(`${API_ENDPOINTS.CREATOR_PRODUCTS.UPDATE}/${productId}`, formData);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Delete product
  deleteProduct: async (productId: string): Promise<{ status: string; message: string }> => {
    try {
      return await apiClient.delete<{ status: string; message: string }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.DELETE}/${productId}`);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Update product specifications
  updateProductSpecifications: async (productId: string, specifications: {
    mainMaterial?: string;
    dressStyle?: string;
    pantType?: string;
    skirtType?: string;
    mensPantSize?: string;
    fitType?: string;
    pattern?: string;
    closure?: string;
    neckline?: string;
    sleeveLength?: string;
    waistline?: string;
    hemline?: string;
  }): Promise<{
    status: string;
    data: { product: Product };
  }> => {
    try {
      return await apiClient.patch<{
        status: string;
        data: { product: Product };
      }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.SPECIFICATIONS}/${productId}/specifications`, specifications);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Update product basic info
  updateProductBasicInfo: async (productId: string, basicInfo: {
    name?: string;
    brand?: string;
    description?: string;
    gender?: string;
    basePrice?: number;
    highlights?: string[];
    tags?: string[];
  }): Promise<{
    status: string;
    data: { product: Product };
  }> => {
    try {
      return await apiClient.patch<{
        status: string;
        data: { product: Product };
      }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.BASIC_INFO}/${productId}/basic-info`, basicInfo);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Add new variation
  addProductVariation: async (productId: string, variation: {
    color: string;
    size: string;
    quantity: number;
    price: number;
    sku?: string;
  }): Promise<{
    status: string;
    data: { product: Product };
  }> => {
    try {
      const formData = new FormData();
      formData.append('color', variation.color);
      formData.append('size', variation.size);
      formData.append('quantity', variation.quantity.toString());
      formData.append('price', variation.price.toString());
      if (variation.sku) {
        formData.append('sku', variation.sku);
      }

      return await apiClient.postFormData<{
        status: string;
        data: { product: Product };
      }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.VARIATIONS}/${productId}/variations`, formData);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Update variation
  updateProductVariation: async (productId: string, variationId: string, variation: {
    color?: string;
    size?: string;
    quantity?: number;
    price?: number;
    salePrice?: number;
    saleStartDate?: string;
    saleEndDate?: string;
    sku?: string;
  }): Promise<{
    status: string;
    data: { product: Product };
  }> => {
    try {
      const formData = new FormData();

      Object.entries(variation).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          formData.append(key, value.toString());
        }
      });

      return await apiClient.patchFormData<{
        status: string;
        data: { product: Product };
      }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.UPDATE_VARIATION}/${productId}/variations/${variationId}`, formData);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Delete variation
  deleteProductVariation: async (productId: string, variationId: string): Promise<{
    status: string;
    message: string;
  }> => {
    try {
      return await apiClient.delete<{
        status: string;
        message: string;
      }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.DELETE_VARIATION}/${productId}/variations/${variationId}`);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Update product variations and related categories
  updateProductVariations: async (productId: string, data: {
    variations: Array<{
      _id?: string;
      color: string;
      size: string;
      quantity: number;
      price: number;
      salePrice?: number;
      saleStartDate?: string;
      saleEndDate?: string;
    }>;
    relatedCategories?: string[];
  }): Promise<{
    status: string;
    data: {
      product: Product;
    };
  }> => {
    try {
      return await apiClient.patch<{
        status: string;
        data: {
          product: Product;
        };
      }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.VARIATIONS}/${productId}/variations`, data);
    } catch (error) {
      throw handleError(error);
    }
  },

  // Update product images
  updateProductImages: async (productId: string, data: {
    images?: string[];
    productImages?: File[];
  }): Promise<{
    status: string;
    message: string;
    data: {
      product: Product;
      summary: {
        totalImages: number;
        addedImages: number;
        removedImages: number;
        uploadedNewImages: string[];
      };
    };
  }> => {
    try {
      // Always use FormData as the API expects images[] form fields
      const formData = new FormData();

      console.log('=== API FormData Debug ===');
      console.log('Images to keep:', data.images);
      console.log('New files to upload:', data.productImages?.map(f => f.name));

      // Add existing images to keep using images[] notation
      if (data.images && data.images.length > 0) {
        data.images.forEach((imageUrl) => {
          formData.append('images[]', imageUrl);
          console.log('Added to FormData images[]:', imageUrl);
        });
      }

      // Add new files to upload (if any)
      if (data.productImages && data.productImages.length > 0) {
        data.productImages.forEach((file) => {
          formData.append('productImages', file);
          console.log('Added to FormData productImages:', file.name);
        });
      }

      // Debug: Log all FormData entries
      console.log('=== Complete FormData ===');
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`${key}: [File] ${value.name}`);
        } else {
          console.log(`${key}: ${value}`);
        }
      }
      console.log('========================');

      return await apiClient.patchFormData<{
        status: string;
        message: string;
        data: {
          product: Product;
          summary: {
            totalImages: number;
            addedImages: number;
            removedImages: number;
            uploadedNewImages: string[];
          };
        };
      }>(`${API_ENDPOINTS.CREATOR_PRODUCTS.IMAGES}/${productId}/images`, formData);
    } catch (error) {
      throw handleError(error);
    }
  },

};

// Apply deduplication to frequently called functions
export const productsApi = {
  ...productsApiBase,
  // Wrap frequently called functions with deduplication
  getCreatorProducts: withDeduplication(
    productsApiBase.getCreatorProducts,
    (params) => `getCreatorProducts:${JSON.stringify(params || {})}`
  ),
  getProductCounts: withDeduplication(
    productsApiBase.getProductCounts,
    (params) => `getProductCounts:${JSON.stringify(params || {})}`
  ),
  getCreatorProduct: withDeduplication(
    productsApiBase.getCreatorProduct,
    (productId) => `getCreatorProduct:${productId}`
  ),
};

// Export for use in other modules
export default productsApi;
