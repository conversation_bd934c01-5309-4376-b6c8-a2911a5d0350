"use client";
import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash, Plus, ChevronLeft } from "lucide-react";
import { useProductForm } from "@/lib/contexts/ProductFormContext";
import { useCreateProduct } from "@/lib/hooks/use-products";
import { useCategoriesHierarchy } from "@/lib/hooks/use-categories";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

// Form validation schema - matching product variations but with identifier instead of color/size
const baleVariationsSchema = z.object({
  variations: z.array(z.object({
    identifier: z.string().min(1, "Identifier is required"),
    quantity: z.coerce.number().min(1, "Quantity must be at least 1"),
    price: z.union([z.string(), z.number()]).pipe(z.coerce.number().min(0.01, "Price must be greater than 0")),
    salePrice: z.union([z.string(), z.number()]).pipe(z.coerce.number().min(0)).optional(),
    saleStartDate: z.string().optional(),
    saleEndDate: z.string().optional(),
  })).min(1, "At least one variation is required"),
  relatedCategories: z.array(z.string()).min(1, "Please select at least one category"),
});

type BaleVariationsFormData = z.infer<typeof baleVariationsSchema>;

interface BaleVariationsFormProps {
  onComplete: () => void;
  onBack: () => void;
}

const BaleVariationsForm = ({ onComplete, onBack }: BaleVariationsFormProps) => {
  const router = useRouter();
  const { variations, productInfo, specifications, updateVariations, resetForm } = useProductForm();
  const { categories } = useCategoriesHierarchy();
  const createProductMutation = useCreateProduct();

  // Local state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("");

  // Form setup
  const form = useForm<BaleVariationsFormData>({
    resolver: zodResolver(baleVariationsSchema),
    defaultValues: {
      variations: variations.variations.length > 0 ? variations.variations : [{
        identifier: "",
        quantity: 1,
        price: 0,
        salePrice: 0,
        saleStartDate: "",
        saleEndDate: "",
      }],
      relatedCategories: variations.relatedCategories && variations.relatedCategories.length > 0
        ? variations.relatedCategories
        : [], // Start with empty array, user must select at least one
    },
  });

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form;

  // Add new variation
  const addVariation = () => {
    const currentVariations = watch('variations') || [];
    setValue('variations', [
      ...currentVariations,
      {
        identifier: "",
        quantity: 1,
        price: 0,
        salePrice: 0,
        saleStartDate: "",
        saleEndDate: "",
      }
    ]);
  };

  // Remove variation
  const removeVariation = (index: number) => {
    const currentVariations = watch('variations') || [];
    if (currentVariations.length > 1) {
      setValue('variations', currentVariations.filter((_, i) => i !== index));
    }
  };

  // Handle related categories
  const addRelatedCategory = () => {
    if (selectedCategory) {
      const currentCategories = watch('relatedCategories') || [];
      if (!currentCategories.includes(selectedCategory)) {
        setValue('relatedCategories', [...currentCategories, selectedCategory]);
      }
      setSelectedCategory("");
    }
  };

  const removeRelatedCategory = (index: number) => {
    const currentCategories = watch('relatedCategories') || [];
    setValue('relatedCategories', currentCategories.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: BaleVariationsFormData) => {
    setIsSubmitting(true);

    try {
      console.log('=== Bale Variations Form Submission ===');
      console.log('Variations form data:', data);

      // Get current context state before updating
      const currentProductInfo = productInfo;
      const currentSpecifications = specifications;

      console.log('Current context state:');
      console.log('- productInfo:', currentProductInfo);
      console.log('- specifications:', currentSpecifications);
      console.log('- variations (new):', data);

      // Manually assemble complete data instead of relying on context update
      const completeData = {
        name: currentProductInfo.name,
        description: currentProductInfo.description,
        basePrice: currentProductInfo.basePrice,
        country: (currentProductInfo as any).country,
        totalItems: (currentProductInfo as any).totalItems,
        weight: (currentProductInfo as any).weight,
        condition: (currentProductInfo as any).condition,
        gender: (currentProductInfo as any).gender,
        dimensions: (currentProductInfo as any).dimensions,
        productImages: currentProductInfo.productImages,
        highlights: currentProductInfo.highlights,
        tags: currentProductInfo.tags,
        specifications: currentSpecifications,
        variations: data.variations,
        relatedCategories: data.relatedCategories,
        type: 'bale' as const,
      };

      console.log('Manually assembled complete data:', completeData);

      // Validate complete data
      if (!completeData.name || !completeData.description) {
        console.error('Missing required fields:', {
          name: completeData.name,
          description: completeData.description
        });
        throw new Error('Missing required bale information');
      }

      if (!completeData.productImages || completeData.productImages.length === 0) {
        console.error('Missing bale images');
        throw new Error('Bale images are required');
      }

      console.log('Creating bale with data:', completeData);

      // Update context for consistency (but don't wait for it)
      updateVariations(data);

      // Create the bale
      await createProductMutation.mutateAsync(completeData);

      // Reset form and redirect
      resetForm();
      onComplete();

    } catch (error) {
      // Error is handled by the mutation
      console.error('Bale creation failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)} className="bg-white mt-2 px-4 py-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-bold mb-6">Bale Variations</h2>

        {/* Variations */}
        <div className="space-y-6">
          {watch('variations')?.map((variation, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium">Variation {index + 1}</h3>
                {watch('variations')?.length > 1 && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    onClick={() => removeVariation(index)}
                  >
                    <Trash className="w-4 h-4" />
                  </Button>
                )}
              </div>

              {/* Basic Fields */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                {/* Identifier */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Identifier <span className="text-red-500">*</span>
                  </label>
                  <Controller
                    name={`variations.${index}.identifier`}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="e.g., BALE-001, LOT-A"
                        className={errors.variations?.[index]?.identifier ? 'border-red-300' : ''}
                      />
                    )}
                  />
                  {errors.variations?.[index]?.identifier && (
                    <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.identifier?.message}</p>
                  )}
                </div>

                {/* Quantity */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Quantity <span className="text-red-500">*</span>
                  </label>
                  <Controller
                    name={`variations.${index}.quantity`}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        min="1"
                        className={errors.variations?.[index]?.quantity ? 'border-red-300' : ''}
                      />
                    )}
                  />
                  {errors.variations?.[index]?.quantity && (
                    <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.quantity?.message}</p>
                  )}
                </div>

                {/* Price */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Price (GHS) <span className="text-red-500">*</span>
                  </label>
                  <Controller
                    name={`variations.${index}.price`}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        className={errors.variations?.[index]?.price ? 'border-red-300' : ''}
                      />
                    )}
                  />
                  {errors.variations?.[index]?.price && (
                    <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.price?.message}</p>
                  )}
                </div>
              </div>

              {/* Sale Price Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Sale Price */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Sale Price (GHS)
                  </label>
                  <Controller
                    name={`variations.${index}.salePrice`}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="Optional sale price"
                        className={errors.variations?.[index]?.salePrice ? 'border-red-300' : ''}
                      />
                    )}
                  />
                  {errors.variations?.[index]?.salePrice && (
                    <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.salePrice?.message}</p>
                  )}
                </div>

                {/* Sale Start Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Sale Start Date
                  </label>
                  <Controller
                    name={`variations.${index}.saleStartDate`}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="datetime-local"
                        className={errors.variations?.[index]?.saleStartDate ? 'border-red-300' : ''}
                      />
                    )}
                  />
                  {errors.variations?.[index]?.saleStartDate && (
                    <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.saleStartDate?.message}</p>
                  )}
                </div>

                {/* Sale End Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    Sale End Date
                  </label>
                  <Controller
                    name={`variations.${index}.saleEndDate`}
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="datetime-local"
                        className={errors.variations?.[index]?.saleEndDate ? 'border-red-300' : ''}
                      />
                    )}
                  />
                  {errors.variations?.[index]?.saleEndDate && (
                    <p className="text-xs text-red-600 mt-1">{errors.variations[index]?.saleEndDate?.message}</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Add Variation Button */}
        <Button
          type="button"
          onClick={addVariation}
          className="mt-4 w-full"
          variant="outline"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Variation
        </Button>

        {/* Related Categories */}
        <div className="mt-8">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Related Categories <span className="text-red-500">*</span>
          </label>
          <p className="text-xs text-gray-500 mb-2">Select at least one category that best describes your bale</p>
          <div className="flex gap-2 mb-2">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select related category" />
              </SelectTrigger>
              <SelectContent>
                {categories?.map((category) => (
                  <SelectItem key={category._id} value={category._id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              type="button"
              onClick={addRelatedCategory}
              disabled={!selectedCategory}
              size="sm"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {watch('relatedCategories')?.map((categoryId, index) => {
              const category = categories?.find(cat => cat._id === categoryId);
              return (
                <Badge key={index} variant="outline" className="flex items-center gap-1">
                  {category?.name || categoryId}
                  <button
                    type="button"
                    onClick={() => removeRelatedCategory(index)}
                    className="ml-1 hover:text-red-500"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              );
            })}
          </div>
          {errors.relatedCategories && (
            <p className="text-xs text-red-600 mt-1">{errors.relatedCategories.message}</p>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="w-4 h-4" />
            Back
          </Button>
          <Button
            type="submit"
            className="px-8"
            disabled={isSubmitting || createProductMutation.isPending}
          >
            {isSubmitting || createProductMutation.isPending ? 'Creating Bale...' : 'Create Bale'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BaleVariationsForm;
