/**
 * API Configuration Module
 * Centralizes API configuration settings and constants
 */

// Base API configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'https://everyfash-api.onrender.com/api/v1',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 1, // Reduced from 3 to 1 to prevent excessive retries
  RETRY_DELAY: 1000, // 1 second
} as const;

// Common headers
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  // Authentication endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER_BUYER: '/auth/register/buyer',
    REGISTER_CREATOR: '/auth/register/creator',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
    RESEND_VERIFICATION: '/auth/resend-verification',
    CHANGE_PASSWORD: '/auth/change-password',
    PROFILE: '/auth/me',
    LOGOUT: '/auth/logout',
    GOOGLE_OAUTH_BUYER: '/auth/google/buyer',
    GOOGLE_OAUTH_CREATOR: '/auth/google/creator',
  },
  
  // Creator onboarding endpoints
  ONBOARDING: {
    STATUS: '/creators/onboarding/status',
    BUSINESS_INFO: '/creators/onboarding/business-info',
    PAYMENT_INFO: '/creators/onboarding/payment-info',
    SHOP_INFO: '/creators/onboarding/shop-info',
    SHIPPING_INFO: '/creators/onboarding/shipping-info',
    VERIFICATION_STATUS: '/creators/onboarding/verification-status',
  },

  // Creator profile endpoints
  PROFILE: {
    BUSINESS_INFO: '/creators/profile/business-info',
    PAYMENT_INFO: '/creators/profile/payment-info',
    SHOP_INFO: '/creators/profile/shop-info',
    SHIPPING_INFO: '/creators/profile/shipping-info',
  },
  
  // Categories endpoints
  CATEGORIES: {
    HIERARCHY: '/categories/hierarchy',
  },

  // Creator products endpoints (for creators)
  CREATOR_PRODUCTS: {
    LIST: '/creators/products',
    CREATE: '/creators/products',
    DETAIL: '/creators/products', // /{id}
    UPDATE: '/creators/products', // /{id}
    DELETE: '/creators/products', // /{id}
    COUNTS: '/creators/products/counts',
    SPECIFICATIONS: '/creators/products', // /{id}/specifications
    BASIC_INFO: '/creators/products', // /{id}/basic-info
    VARIATIONS: '/creators/products', // /{id}/variations
    UPDATE_VARIATION: '/creators/products', // /{id}/variations/{variationId}
    DELETE_VARIATION: '/creators/products', // /{id}/variations/{variationId}
    IMAGES: '/creators/products', // /{id}/images
  },

  // Public products endpoints (for buyers)
  PRODUCTS: {
    LIST: '/products',
    DETAIL: '/products', // /{id}
    SEARCH: '/products/search',
    FILTER: '/products/filter',
  },
} as const;

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Authentication redirect URLs
export const AUTH_REDIRECTS = {
  BUYER_LOGIN: '/login',
  CREATOR_LOGIN: '/creators/login',
  BUYER_DASHBOARD: '/',
  CREATOR_DASHBOARD: '/creators',
} as const;
